
# 知识图谱功能实现
本系统涉及的m*b 类型包括四大类，分别为常用设施、XX平台、有生LL、重要DHY等四大类，MB拥有属性项、子MB、与其他MB的关联关系等。需要多源异构数据中识别出MB、行为、时空关系等构建系统的本体模型，进而将要素通过相互作用的方式系统整合，形成mb关系的知识关联网络。通过对半结构化数据和结构化数据进行实体抽取、属性抽取、关联关系抽取、属性对齐、实体对齐、关系对齐等知识融合步骤，最终实现实体管理以及知识图谱的可视化形式展示、查询等。
#### 知识抽取
**输入数据**：
①半结构化数据（xx.doc、xx.xlxs、xx.csv）
②结构化数据（从数据库中直接读取，备份数据库物理隔绝）（通过api从已有的结构化数据库中获取数据，格式为json格式）

**抽取要素**：
四大类MB的名称、国别、位置等。以下详细举例：

①常用设施：

实体名称（华能YC热电公司）、所属国家地区（中国）、具体位置（锦山街锦山公路15号）、纬度（47°44′18″N）、经度（128°45′11″E）、地位作用（作为一家大型xxx企业，xxxx起到了至关重要的保障作用）、历史沿革（xxx成立于xx年xx月......）等

②xx平台
实体名称（xxx船）、国家地区（隶属）（韩国）、舷号（500xx）、隶属关系（x国东海海警署）、长（米）、宽（米）、续航（海里/节）等

③xxDHY
实体名称（东岛）、国家地区（驻地）、国家地区（隶属）、位置描述（东岛位于永兴岛东南约xx海里处）、纬度（16°23′34″N）、经度（112°23′22″E）、地位作用等

④有生LL
实体名称（蔡xx）、国家地区（隶属）、持有国际（xx国）、籍贯、民族、性别、身高（厘米）、体重（千克）、肤色、当前职务、ZZ立场、其他社会关系（民X党xx是其ZZ领路人，曾服务过xxx等分子）、党级、任职经历等

注：括号内为举例信息

**要求：抽取要素可配置，按照类型配置抽取要素。需要明确配置后的数据项从哪张表读取。**
**输出：**抽取任务列表、抽取详情
**功能：**知识抽取列表页面分页查询展示、新增任务、删除任务、开始、暂停 、终止任务。查看抽取结果。
**界面效果：**

抽取列表页面

新增抽取任务弹窗

抽取实体和属性结果

抽取关系结果

#### （二）配置抽取要素
不同类型的实体，抽取的要素不同。通过抽取要素配置功能实现要素配置。
进入属性配置页面，选择类型树，在右侧列表中，点击“设置为抽取属性”按钮，将属性设置为抽取要素。


#### （三）实体管理
**功能**：实体管理列表分页查询展示；点击详情可查看实体的属性、关联关系；点击编辑按钮可进行修改。
**界面效果**如下：




#### （四）图谱可视化
点击图谱可视化，展示知识图谱拓扑图，在右侧筛选工具中可进行类型选择、区域选择、名称输入查询、展示关系选择和层级选择。

#### （五）综合分析
选择节点，计算与该节点同类型的节点的最短路径。例如，xx电厂故障，需要计算其他与该电厂同类型的、路径最短的电厂实体，通过高亮展示实体以及实体关系线。可也通过筛选层级和关系类型进行条件筛选。
