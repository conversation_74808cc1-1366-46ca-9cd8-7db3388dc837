# 酒店防疫智慧管理系统API接口文档

## 1. 用户认证模块

### 1.1 用户登录

接口地址：`POST /api/auth/login`

功能描述：管理员登录系统

请求参数：

```json
{
  "username": "admin",
  "password": "password123"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

响应示例：

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": "admin001",
      "username": "admin",
      "name": "系统管理员",
      "role": "admin"
    }
  }
}
```

---

## 2. 数据统计模块

### 2.1 获取隔离房间数统计

接口地址：`GET /api/statistics/isolation-rooms`

功能描述：统计酒店中已用隔离房间数

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "isolationRooms": 85,
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

### 2.2 获取房间总数统计

接口地址：`GET /api/statistics/total-rooms`

功能描述：统计酒店中房间总数

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalRooms": 200,
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

### 2.3 获取空置房间数统计

接口地址：`GET /api/statistics/vacant-rooms`

功能描述：统计酒店中空置房间数

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "vacantRooms": 115,
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

---

## 3. 房间状态控制模块

### 3.1 根据房号控制房间状态

接口地址：`PUT /api/room/control/by-number/{roomNumber}`

功能描述：根据房号控制房间状态

请求参数：

```json
{
  "status": "occupied"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roomNumber | string | 是 | 房间号（路径参数） |
| status | string | 是 | 房间状态 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "房间状态更新成功",
  "data": {
    "roomNumber": "101",
    "status": "occupied",
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

### 3.2 根据楼层控制房间状态

接口地址：`PUT /api/room/control/by-floor/{floor}`

功能描述：根据楼层控制房间状态

请求参数：

```json
{
  "status": "maintenance"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| floor | integer | 是 | 楼层（路径参数） |
| status | string | 是 | 房间状态 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "楼层房间状态更新成功",
  "data": {
    "floor": 3,
    "affectedRooms": 20,
    "status": "maintenance",
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

### 3.3 一键控制房间状态

接口地址：`PUT /api/room/control/batch`

功能描述：一键控制所有房间状态

请求参数：

```json
{
  "status": "available"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 房间状态 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "批量房间状态更新成功",
  "data": {
    "totalRooms": 200,
    "affectedRooms": 200,
    "status": "available",
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

---

## 4. 设备开关控制模块

### 4.1 根据房号控制设备开关状态

接口地址：`PUT /api/device/control/by-room/{roomNumber}`

功能描述：根据房号控制设备开关状态

请求参数：

```json
{
  "deviceType": "air_conditioner",
  "status": true
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roomNumber | string | 是 | 房间号（路径参数） |
| deviceType | string | 是 | 设备类型 |
| status | boolean | 是 | 开关状态 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "设备控制成功",
  "data": {
    "roomNumber": "101",
    "deviceType": "air_conditioner",
    "status": true,
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

### 4.2 根据楼层控制设备开关状态

接口地址：`PUT /api/device/control/by-floor/{floor}`

功能描述：根据楼层控制设备开关状态

请求参数：

```json
{
  "deviceType": "lighting",
  "status": false
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| floor | integer | 是 | 楼层（路径参数） |
| deviceType | string | 是 | 设备类型 |
| status | boolean | 是 | 开关状态 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "楼层设备控制成功",
  "data": {
    "floor": 3,
    "deviceType": "lighting",
    "affectedDevices": 20,
    "status": false,
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

### 4.3 一键控制设备开关状态

接口地址：`PUT /api/device/control/batch`

功能描述：一键控制所有设备开关状态

请求参数：

```json
{
  "deviceType": "ventilation",
  "status": true
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deviceType | string | 是 | 设备类型 |
| status | boolean | 是 | 开关状态 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "批量设备控制成功",
  "data": {
    "deviceType": "ventilation",
    "totalDevices": 200,
    "affectedDevices": 200,
    "status": true,
    "updateTime": "2024-01-15T10:30:00Z"
  }
}
```

---

## 5. 酒店摄像头接入模块

### 5.1 获取一脱区和二脱区实时监控画面

接口地址：`GET /api/camera/live-stream/decontamination-areas`

功能描述：获取酒店中一脱区和二脱区实时监控画面

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "decontaminationArea1": {
      "cameraId": "cam_001",
      "streamUrl": "rtmp://stream.example.com/live/area1",
      "status": "online"
    },
    "decontaminationArea2": {
      "cameraId": "cam_002",
      "streamUrl": "rtmp://stream.example.com/live/area2",
      "status": "online"
    }
  }
}
```

### 5.2 获取摄像头点位标注

接口地址：`GET /api/camera/positions`

功能描述：获取酒店中摄像头点位标注信息

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "cameraId": "cam_001",
      "name": "大堂监控",
      "position": {
        "x": 100,
        "y": 200
      },
      "floor": 1,
      "area": "lobby"
    },
    {
      "cameraId": "cam_002",
      "name": "走廊监控",
      "position": {
        "x": 300,
        "y": 150
      },
      "floor": 2,
      "area": "corridor"
    }
  ]
}
```

### 5.3 实时查看摄像头画面

接口地址：`GET /api/camera/live-stream/{cameraId}`

功能描述：实时查看指定摄像头画面

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cameraId | string | 是 | 摄像头ID（路径参数） |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "cameraId": "cam_001",
    "streamUrl": "rtmp://stream.example.com/live/cam_001",
    "status": "online",
    "resolution": "1920x1080",
    "fps": 25
  }
}
```

### 5.4 房间异常时自动拉取摄像头画面

接口地址：`GET /api/camera/auto-pull/{roomNumber}`

功能描述：房间异常状态时自动拉取房间附近摄像头画面

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roomNumber | string | 是 | 房间号（路径参数） |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "roomNumber": "101",
    "nearbyCameras": [
      {
        "cameraId": "cam_101_door",
        "streamUrl": "rtmp://stream.example.com/live/cam_101_door",
        "position": "门口"
      },
      {
        "cameraId": "cam_corridor_1f",
        "streamUrl": "rtmp://stream.example.com/live/cam_corridor_1f",
        "position": "走廊"
      }
    ]
  }
}
```

---

## 6. 平台预警模块

### 6.1 房间异常状态预警

接口地址：`GET /api/alert/room-abnormal`

功能描述：获取房间异常状态预警信息

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "alertId": "alert_001",
      "roomNumber": "101",
      "alertType": "door_open_overtime",
      "alertTime": "2024-01-15T10:30:00Z",
      "status": "pending",
      "description": "房间门开启超时"
    },
    {
      "alertId": "alert_002",
      "roomNumber": "205",
      "alertType": "temperature_abnormal",
      "alertTime": "2024-01-15T10:25:00Z",
      "status": "processing",
      "description": "房间温度异常"
    }
  ]
}
```

### 6.2 根据日期展示房间报警记录列表

接口地址：`GET /api/alert/room-records`

功能描述：根据日期展示房间报警记录列表

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始日期 |
| endDate | string | 是 | 结束日期 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 25,
    "records": [
      {
        "recordId": "record_001",
        "roomNumber": "101",
        "alertType": "door_open_overtime",
        "alertTime": "2024-01-15T10:30:00Z",
        "handleStatus": "completed",
        "handler": "张管理员",
        "handleTime": "2024-01-15T10:35:00Z"
      }
    ]
  }
}
```

### 6.3 管理员处理房间报警记录

接口地址：`PUT /api/alert/room-records/{recordId}/handle`

功能描述：管理员处理房间报警记录

请求参数：

```json
{
  "handleResult": "已处理",
  "handleNote": "已联系房客关闭房门"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | string | 是 | 记录ID（路径参数） |
| handleResult | string | 是 | 处理结果 |
| handleNote | string | 否 | 处理备注 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "处理成功",
  "data": {
    "recordId": "record_001",
    "handleStatus": "completed",
    "handler": "张管理员",
    "handleTime": "2024-01-15T10:35:00Z"
  }
}
```

### 6.4 根据日期展示用户测温记录列表

接口地址：`GET /api/alert/temperature-records`

功能描述：根据日期展示用户测温记录列表

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始日期 |
| endDate | string | 是 | 结束日期 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 150,
    "records": [
      {
        "recordId": "temp_001",
        "guestId": "guest_001",
        "guestName": "张三",
        "roomNumber": "101",
        "temperature": 36.5,
        "measureTime": "2024-01-15T08:00:00Z",
        "status": "normal"
      },
      {
        "recordId": "temp_002",
        "guestId": "guest_002",
        "guestName": "李四",
        "roomNumber": "102",
        "temperature": 37.8,
        "measureTime": "2024-01-15T08:05:00Z",
        "status": "abnormal"
      }
    ]
  }
}
```

### 6.5 测温记录中用户体温异常时预警

接口地址：`GET /api/alert/temperature-abnormal`

功能描述：检测测温记录中用户体温异常并预警

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "alertId": "temp_alert_001",
      "guestId": "guest_002",
      "guestName": "李四",
      "roomNumber": "102",
      "temperature": 37.8,
      "measureTime": "2024-01-15T08:05:00Z",
      "alertLevel": "high",
      "status": "pending"
    }
  ]
}
```

### 6.6 实时获取酒店中当前开门房间列表

接口地址：`GET /api/alert/open-doors`

功能描述：实时获取酒店中当前开门房间列表

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "roomNumber": "101",
      "guestName": "张三",
      "openTime": "2024-01-15T10:30:00Z",
      "duration": "00:05:30",
      "status": "open"
    },
    {
      "roomNumber": "205",
      "guestName": "王五",
      "openTime": "2024-01-15T10:25:00Z",
      "duration": "00:10:15",
      "status": "open"
    }
  ]
}
```

---

## 7. 数据导出模块

### 7.1 根据日期导出测温记录列表

接口地址：`GET /api/export/temperature-records`

功能描述：根据日期导出测温记录列表

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始日期 |
| endDate | string | 是 | 结束日期 |
| format | string | 否 | 导出格式（excel/csv） |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "downloadUrl": "https://example.com/downloads/temp_records_20240115.xlsx",
    "fileName": "temp_records_20240115.xlsx",
    "fileSize": "2.5MB",
    "recordCount": 150
  }
}
```

---

## 8. 房间信息模块

### 8.1 展示酒店房间列表

接口地址：`GET /api/room/list`

功能描述：展示酒店房间列表

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| floor | integer | 否 | 楼层筛选 |
| status | string | 否 | 状态筛选 |
| pageNum | integer | 否 | 页码 |
| pageSize | integer | 否 | 页大小 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 200,
    "pageNum": 1,
    "pageSize": 20,
    "rooms": [
      {
        "roomId": "room_001",
        "roomNumber": "101",
        "floor": 1,
        "status": "occupied",
        "guestName": "张三",
        "checkInTime": "2024-01-10T14:00:00Z",
        "lastActivity": "2024-01-15T10:30:00Z"
      },
      {
        "roomId": "room_002",
        "roomNumber": "102",
        "floor": 1,
        "status": "available",
        "guestName": null,
        "checkInTime": null,
        "lastActivity": "2024-01-15T09:00:00Z"
      }
    ]
  }
}
```

### 8.2 房间开门状态实时监控

接口地址：`GET /api/room/door-status/monitor`

功能描述：房间开门状态实时监控

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalRooms": 200,
    "openRooms": 5,
    "closedRooms": 195,
    "roomStatus": [
      {
        "roomNumber": "101",
        "doorStatus": "open",
        "openTime": "2024-01-15T10:30:00Z",
        "duration": "00:05:30"
      },
      {
        "roomNumber": "102",
        "doorStatus": "closed",
        "lastOpenTime": "2024-01-15T08:00:00Z"
      }
    ]
  }
}
```

---

## 9. 信息登记与房间分配模块

### 9.1 房客手机登记信息并自动分配房间

接口地址：`POST /api/registration/mobile-register`

功能描述：房客通过手机登记信息并自动分配房间

请求参数：

```json
{
  "name": "张三",
  "idCard": "110101199001011234",
  "phone": "13800138000",
  "emergencyContact": "李四",
  "emergencyPhone": "13900139000"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 姓名 |
| idCard | string | 是 | 身份证号 |
| phone | string | 是 | 手机号 |
| emergencyContact | string | 否 | 紧急联系人 |
| emergencyPhone | string | 否 | 紧急联系电话 |

响应示例：

```json
{
  "code": 200,
  "message": "登记成功",
  "data": {
    "guestId": "guest_001",
    "name": "张三",
    "assignedRoom": "101",
    "checkInCode": "ABC123",
    "registrationTime": "2024-01-15T10:30:00Z"
  }
}
```

### 9.2 快速入住登记与身份核验

接口地址：`POST /api/registration/quick-checkin`

功能描述：扫描二维码/身份证，快速调取预登记信息完成入住登记

请求参数：

```json
{
  "qrCode": "ABC123",
  "idCard": "110101199001011234"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| qrCode | string | 否 | 二维码内容 |
| idCard | string | 否 | 身份证号 |

响应示例：

```json
{
  "code": 200,
  "message": "入住成功",
  "data": {
    "guestId": "guest_001",
    "name": "张三",
    "roomNumber": "101",
    "checkInTime": "2024-01-15T10:30:00Z",
    "expectedCheckOut": "2024-01-29T10:30:00Z"
  }
}
```

### 9.3 流调信息预接收入口

接口地址：`POST /api/registration/epidemiological-info`

功能描述：接收区级平台下派的待入住人员信息和风险评估

请求参数：

```json
{
  "personInfo": {
    "name": "王五",
    "idCard": "110101199002021234",
    "phone": "13700137000",
    "address": "北京市朝阳区"
  },
  "riskAssessment": {
    "riskLevel": "medium",
    "contactHistory": "密接",
    "lastExposureDate": "2024-01-13T10:00:00Z"
  }
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| personInfo | object | 是 | 人员信息 |
| riskAssessment | object | 是 | 风险评估 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "信息接收成功",
  "data": {
    "recordId": "epi_001",
    "status": "pending_assignment",
    "estimatedArrival": "2024-01-16T14:00:00Z"
  }
}
```

---

## 10. 电子告知书签字确认模块

### 10.1 电子告知书签字确认

接口地址：`POST /api/consent/electronic-signature`

功能描述：房客手机完成隔离规定、知情同意书的签名确认，存档

请求参数：

```json
{
  "guestId": "guest_001",
  "consentType": "isolation_agreement",
  "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "signatureTime": "2024-01-15T10:30:00Z"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| guestId | string | 是 | 房客ID |
| consentType | string | 是 | 同意书类型 |
| signature | string | 是 | 电子签名 |
| signatureTime | string | 是 | 签名时间 |

响应示例：

```json
{
  "code": 200,
  "message": "签名确认成功",
  "data": {
    "consentId": "consent_001",
    "guestId": "guest_001",
    "consentType": "isolation_agreement",
    "signatureTime": "2024-01-15T10:30:00Z",
    "status": "confirmed"
  }
}
```

---

## 11. 全生命周期人员档案模块

### 11.1 创建人员档案

接口地址：`POST /api/guest/profile`

功能描述：创建隔离人员的完整档案信息

请求参数：

```json
{
  "personalInfo": {
    "name": "张三",
    "idCard": "110101199001011234",
    "phone": "13800138000",
    "age": 30,
    "gender": "male"
  },
  "healthInfo": {
    "temperature": 36.5,
    "symptoms": "无",
    "chronicDiseases": "无",
    "allergies": "无"
  },
  "riskAssessment": {
    "riskLevel": "medium",
    "contactHistory": "密接",
    "exposureDate": "2024-01-13T10:00:00Z"
  }
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| personalInfo | object | 是 | 个人信息 |
| healthInfo | object | 是 | 健康信息 |
| riskAssessment | object | 是 | 风险评估 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "档案创建成功",
  "data": {
    "guestId": "guest_001",
    "profileId": "profile_001",
    "name": "张三",
    "status": "active",
    "createdTime": "2024-01-15T10:30:00Z"
  }
}
```

### 11.2 获取人员档案详情

接口地址：`GET /api/guest/profile/{guestId}`

功能描述：获取隔离人员的完整档案详情

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| guestId | string | 是 | 房客ID（路径参数） |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "guestId": "guest_001",
    "personalInfo": {
      "name": "张三",
      "idCard": "110101199001011234",
      "phone": "13800138000",
      "age": 30,
      "gender": "male"
    },
    "healthInfo": {
      "currentTemperature": 36.5,
      "symptoms": "无",
      "chronicDiseases": "无",
      "allergies": "无",
      "lastCheckTime": "2024-01-15T08:00:00Z"
    },
    "accommodationInfo": {
      "roomNumber": "101",
      "checkInTime": "2024-01-10T14:00:00Z",
      "expectedCheckOut": "2024-01-24T14:00:00Z"
    },
    "riskAssessment": {
      "riskLevel": "medium",
      "contactHistory": "密接",
      "exposureDate": "2024-01-13T10:00:00Z"
    }
  }
}
```

### 11.3 更新人员档案

接口地址：`PUT /api/guest/profile/{guestId}`

功能描述：更新隔离人员档案信息

请求参数：

```json
{
  "updateInfo": {
    "healthInfo": {
      "temperature": 36.8,
      "symptoms": "轻微咳嗽",
      "checkTime": "2024-01-15T08:00:00Z"
    }
  }
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| guestId | string | 是 | 房客ID（路径参数） |
| updateInfo | object | 是 | 更新信息 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "档案更新成功",
  "data": {
    "guestId": "guest_001",
    "updateTime": "2024-01-15T10:30:00Z",
    "updatedFields": ["healthInfo"]
  }
}
```

---

## 12. 每日健康申报打卡模块

### 12.1 提交每日健康申报

接口地址：`POST /api/health/daily-report`

功能描述：房客通过手机端每日提交体温、症状、抗原结果等信息

请求参数：

```json
{
  "guestId": "guest_001",
  "temperature": 36.5,
  "symptoms": "无",
  "antigenResult": "阴性",
  "reportTime": "2024-01-15T08:00:00Z"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| guestId | string | 是 | 房客ID |
| temperature | number | 是 | 体温 |
| symptoms | string | 否 | 症状描述 |
| antigenResult | string | 否 | 抗原结果 |
| reportTime | string | 是 | 申报时间 |

响应示例：

```json
{
  "code": 200,
  "message": "健康申报成功",
  "data": {
    "reportId": "report_001",
    "guestId": "guest_001",
    "reportTime": "2024-01-15T08:00:00Z",
    "status": "normal"
  }
}
```

### 12.2 获取健康申报记录

接口地址：`GET /api/health/daily-report/records`

功能描述：获取房客的健康申报记录

请求参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| guestId | string | 是 | 房客ID |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |
| Authorization | string | 否 | Bearer token |

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 5,
    "records": [
      {
        "reportId": "report_001",
        "reportDate": "2024-01-15",
        "temperature": 36.5,
        "symptoms": "无",
        "antigenResult": "阴性",
        "status": "normal"
      },
      {
        "reportId": "report_002",
        "reportDate": "2024-01-14",
        "temperature": 36.3,
        "symptoms": "无",
        "antigenResult": "阴性",
        "status": "normal"
      }
    ]
  }
}
```

---

## 13. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权访问 | 检查Token是否有效 |
| 403 | 权限不足 | 联系管理员分配相应权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

---

## 14. 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

**字段说明：**
- code: 状态码
- message: 响应消息
- data: 响应数据

---

## 15. 认证说明

除登录接口外，所有接口都需要在请求头中携带认证信息：

```
Authorization: Bearer <your_jwt_token>
```

Token有效期为24小时，过期后需要重新登录获取新的Token。

---

## 16. 版本更新记录

### v1.0.0 (2024-01-15)
- 初始版本发布
- 包含用户认证、数据统计、房间控制等基础功能
- 支持设备控制、摄像头监控、预警管理
- 提供人员档案、健康申报等核心业务功能

---

**联系方式：**
- 技术支持：<EMAIL>
- 开发团队：<EMAIL>
