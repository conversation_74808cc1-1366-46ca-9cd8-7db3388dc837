#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档转Markdown脚本
支持.docx和.doc格式文件
"""

import os
import sys
from pathlib import Path

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
except ImportError:
    print("需要安装python-docx库: pip install python-docx")
    sys.exit(1)

try:
    import docx2txt
except ImportError:
    print("需要安装docx2txt库: pip install docx2txt")
    sys.exit(1)

def extract_text_from_docx(docx_path):
    """从.docx文件提取文本并转换为Markdown"""
    try:
        doc = Document(docx_path)
        markdown_content = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if not text:
                markdown_content.append("")
                continue
            
            # 检查段落样式
            style_name = paragraph.style.name.lower()
            
            # 处理标题
            if 'heading' in style_name:
                if 'heading 1' in style_name:
                    markdown_content.append(f"# {text}")
                elif 'heading 2' in style_name:
                    markdown_content.append(f"## {text}")
                elif 'heading 3' in style_name:
                    markdown_content.append(f"### {text}")
                elif 'heading 4' in style_name:
                    markdown_content.append(f"#### {text}")
                elif 'heading 5' in style_name:
                    markdown_content.append(f"##### {text}")
                elif 'heading 6' in style_name:
                    markdown_content.append(f"###### {text}")
                else:
                    markdown_content.append(f"## {text}")
            else:
                # 处理普通段落
                formatted_text = text
                
                # 检查段落中的格式化文本
                for run in paragraph.runs:
                    if run.bold and run.text.strip():
                        formatted_text = formatted_text.replace(run.text, f"**{run.text}**")
                    elif run.italic and run.text.strip():
                        formatted_text = formatted_text.replace(run.text, f"*{run.text}*")
                
                markdown_content.append(formatted_text)
        
        # 处理表格
        for table in doc.tables:
            markdown_content.append("")  # 空行
            
            # 表头
            if table.rows:
                header_row = table.rows[0]
                header_cells = [cell.text.strip() for cell in header_row.cells]
                markdown_content.append("| " + " | ".join(header_cells) + " |")
                markdown_content.append("| " + " | ".join(["---"] * len(header_cells)) + " |")
                
                # 数据行
                for row in table.rows[1:]:
                    data_cells = [cell.text.strip() for cell in row.cells]
                    markdown_content.append("| " + " | ".join(data_cells) + " |")
            
            markdown_content.append("")  # 空行
        
        return "\n".join(markdown_content)
    
    except Exception as e:
        print(f"处理.docx文件时出错: {e}")
        return None

def extract_text_from_doc(doc_path):
    """从.doc文件提取文本并转换为Markdown"""
    try:
        # 使用docx2txt处理.doc文件
        text = docx2txt.process(doc_path)
        
        # 简单的Markdown转换
        lines = text.split('\n')
        markdown_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                markdown_content.append("")
                continue
            
            # 简单的标题检测（基于文本特征）
            if len(line) < 50 and (
                line.isupper() or 
                any(keyword in line for keyword in ['第', '章', '节', '部分', '概述', '介绍', '总结'])
            ):
                markdown_content.append(f"## {line}")
            else:
                markdown_content.append(line)
        
        return "\n".join(markdown_content)
    
    except Exception as e:
        print(f"处理.doc文件时出错: {e}")
        return None

def convert_word_to_markdown(word_file_path, output_dir=None):
    """将Word文档转换为Markdown"""
    word_path = Path(word_file_path)
    
    if not word_path.exists():
        print(f"文件不存在: {word_file_path}")
        return False
    
    # 确定输出目录
    if output_dir is None:
        output_dir = word_path.parent
    else:
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成输出文件名
    output_file = output_dir / f"{word_path.stem}.md"
    
    print(f"正在转换: {word_file_path}")
    
    # 根据文件扩展名选择处理方法
    if word_path.suffix.lower() == '.docx':
        markdown_content = extract_text_from_docx(word_file_path)
    elif word_path.suffix.lower() == '.doc':
        markdown_content = extract_text_from_doc(word_file_path)
    else:
        print(f"不支持的文件格式: {word_path.suffix}")
        return False
    
    if markdown_content is None:
        print(f"转换失败: {word_file_path}")
        return False
    
    # 保存Markdown文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        print(f"转换完成: {output_file}")
        return True
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False

def main():
    """主函数"""
    # 当前目录下的Word文档
    word_files = [
        "接口对接.docx",
        "知识图谱实现.doc"
    ]
    
    print("开始转换Word文档为Markdown...")
    
    success_count = 0
    for word_file in word_files:
        if os.path.exists(word_file):
            if convert_word_to_markdown(word_file):
                success_count += 1
        else:
            print(f"文件不存在: {word_file}")
    
    print(f"\n转换完成! 成功转换 {success_count}/{len(word_files)} 个文件")

if __name__ == "__main__":
    main()
