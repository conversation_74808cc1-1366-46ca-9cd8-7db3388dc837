接口对接
实体管理接口对接：
![图片1](images/image_1.png)


查询实体
IP_dir: http://127.0.0.1:23232/entity_manage_query
请求方式：POST
request:
{
"entity_name":"金牛炼铁厂",   #实体名称  必须填写
"entity_type":"炼铁厂"       #实体类型   必须填写
}

response:
{
"nerbor_rel": [                                     #实体邻近关系列表
{
"direction": "outgoing",       #Outgoing代表 A->B ,incoming代表B->A

"end_node": {
"实体名称": "金牛采矿场",
"市值": "10亿",
"经营性质": "国营",
"负责人": "赵权",
"资质": "已办理"
},
"relation_type": "提供"   #关系类型
},
{
"direction": "ingoing",
"end_node": {
"实体名称": "鸿翔运输"
},
"relation_type": "保障"
}
],
"nerbor_rel_count": 2,  #邻近关系数量
"properties": {                          #查询实体自己的属性
"实体名称": "金牛炼铁厂"
},
"properties_count": 1   #实体属性数量
}




删除实体
ip_dir : http://127.0.0.1:23232/entity_manage_delete
请求方式：POST
request:
{
"entity_name":"金牛炼铁厂",   #实体名称  必须填写
"entity_type":"炼铁厂"       #实体类型   必须填写
}

response:
{
"status":True/False     #True代表成功，False代表失败
            }


![图片2](images/image_2.png)

编辑实体属性
ip_dir:  http://127.0.0.1:23232/entity_manage_edit_properties
请求方式：POST
request:
{
"entity_name":"金牛炼铁厂",  #必填
"entity_type":"炼铁厂",      #必填
"properties":{             #实体属性
"管理员": "刘心",        #管理员是属性名称，刘心是属性值， 单位统一到属性值中，比如"167个"为属性值
"mb等级": "2"
}
}
reponse:
{
"status": **true**
}
删除实体属性
ip_dir:   http://127.0.0.1:23232/entity_manage_delete_properties

请求方式:POST
request:
{
"entity_name":"金牛炼铁厂",  #必填
"entity_type":"炼铁厂",      #必填
"property_key":"管理员"                  #必填
}
response:
{
"status":True/False
}


![图片3](images/image_3.png)


编辑实体关系
ip_dir:  http://127.0.0.1:23232/entity_manage_edit_rel
请求方式：POST
request:
{
"from_name":"天府机场",      #实体名称
"to_name":"彭州石化",         #关联实体
"rel_type":"后勤",                #关系
"direction":"A->B"           #指向   只支持A->B或者B->A
}

response:
{
"status":True/False
}
6、删除实体关系
ip_dir:  http://127.0.0.1:23232/entity_manage_delete_rel

请求方式：POST
request:
{
"from_name":"天府机场",  #实体名称
"to_name":"彭州石化"    #关联实体
}
response:
{
"status":True/False
}

7、新增实体关系
ip_dir:  http://127.0.0.1:23232/entity_manage_add_rel

请求方式：POST
request:
{
"start_entity_type":"机场",   #实体类型
"start_entity_name":"天府机场",   #实体名称
"end_entity_type":"石化设施",    #关联实体类型
"end_entity_name":"彭州石化",    #关联实体名称
"relation_type":"保障",             #关系类型
"direction":"A->B"     #指向    只有A->B和B->A
}

response:
{
"status":True/False
}


###################################################################
知识抽取
要素抽取
查找实体及属性

####################################################################
1、知识抽取，新增知识
p_dir:  http://127.0.0.1:23232/knowledge_add

请求方式：POST
request:
{"triplets":
[
{
'startNodeLabel': '采矿场',                                  #起始实体类型， 必填
'startNodeProps': {'实体名称': '金牛采矿场'},       #起始实体名称，必填
'relType': '提供',                                                   #关系类型       必填
'endNodeLabel': '炼铁厂',                                   #结束实体类型   必填
'endNodeProps': {'实体名称': '金牛炼铁厂'},       #结束实体名称 必填
"direction": "Mb1->Mb2"                                #方向    只有 Mb1->Mb2, Mb1<-Mb2，这两种关系 ，Mb1代表头，Mb2代表尾
},
{
'startNodeLabel': '运输公司',
'startNodeProps': {'实体名称': '鸿翔运输'},
'relType': '保障',
'endNodeLabel': '炼铁厂',
'endNodeProps': {'实体名称': '金牛炼铁厂'},
"direction": "Mb1<-Mb2"
}
]
}

example:
{
"triplets": [
{
"startNodeLabel": "电厂",
"startNodeProps": {
"实体名称": "金牛电厂"
},
"relType": "提供",
"endNodeLabel": "工厂",
"endNodeProps": {
"实体名称": "金牛螺丝厂"
},
"direction": "Mb1->Mb2"
},
{
"startNodeLabel": "工厂",
"startNodeProps": {
"实体名称": "金牛螺丝厂"
},
"relType": "保障",
"endNodeLabel": "船厂",
"endNodeProps": {
"实体名称": "金牛船厂"
},
"direction": "Mb1<-Mb2"
}
]
}


response:
{
"status":True/False
}

要素抽取，新增要素
p_dir:  http://127.0.0.1:23232/properties_add


请求方式：POST
request:
{"data":[
{
"ent_type": "电厂",   #实体类型
"实体名称": "金牛电厂",  #实体名称
"负责人": "赵权",      #负责人 要素类型，赵权是要素值
"资质": "已办理",
"市值":"10亿"
}
]
}
response:
{
"status":True/False
}

查找所有实体及属性
p_dir:  http://127.0.0.1:23232/knowledge_query


请求方式：POST
request:
{
"data":"all"

}
response:
{
"data": [
{
"properties": {         #实体属性
"mb别称": "测试MB别称",
"mb名称": "测试MB名称",
"mb等级": "2",
"实体名称": "金牛机场",     #实体名称
"所在县区级行政区划": "武侯区",
"所在国家（地区）": "jk",
"所在地市级行政区划": "空中楼阁",
"所在省级行政区划": 123,
"是否为Z勤MB": 123,
"识别码": 123456
},
"实体类型": "机场"    #实体类型
},
{
"properties": {
"实体名称": "金牛采矿场",
"市值": "10亿",
"经营性质": "国营",
"负责人": "赵权",
"资质": "已办理"
},
"实体类型": "采矿场"
}
]
}




